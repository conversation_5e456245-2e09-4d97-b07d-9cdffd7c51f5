{"/projects/page": "app/projects/page.js", "/projects/[slug]/page": "app/projects/[slug]/page.js", "/version-history/page": "app/version-history/page.js", "/blogs/[slug]/page": "app/blogs/[slug]/page.js", "/page": "app/page.js", "/_not-found/page": "app/_not-found/page.js", "/api/waline/comment/count/route": "app/api/waline/comment/count/route.js", "/api/visit-stats/route": "app/api/visit-stats/route.js", "/blogs/page": "app/blogs/page.js", "/api/waline/pageview/route": "app/api/waline/pageview/route.js"}