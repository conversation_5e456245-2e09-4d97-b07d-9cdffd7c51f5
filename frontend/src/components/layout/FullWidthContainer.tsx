import { forwardRef } from 'react'
import clsx from 'clsx'

// 全宽布局容器类型定义
export interface FullWidthContainerProps extends React.ComponentPropsWithoutRef<'div'> {
  variant?: 'full' | 'reading' | 'showcase' | 'content'
  background?: 'none' | 'subtle' | 'gradient' | 'glass'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  maxWidth?: 'none' | 'reading' | 'wide' | 'ultra'
}

export interface SectionContainerProps extends React.ComponentPropsWithoutRef<'section'> {
  variant?: 'hero' | 'content' | 'sidebar' | 'footer'
  background?: 'none' | 'subtle' | 'accent' | 'primary'
  spacing?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
}

export interface ContentContainerProps extends React.ComponentPropsWithoutRef<'div'> {
  layout?: 'single' | 'sidebar' | 'wide' | 'split'
  align?: 'left' | 'center' | 'right'
  spacing?: 'tight' | 'normal' | 'loose'
}

// 🎨 全宽布局容器 - 红点奖级别设计
export const FullWidthContainer = forwardRef<HTMLDivElement, FullWidthContainerProps>(
  ({ 
    variant = 'full', 
    background = 'none', 
    padding = 'md', 
    maxWidth = 'none',
    className, 
    children, 
    ...props 
  }, ref) => {
    
    // 容器变体样式
    const variantClasses = {
      full: 'w-full min-h-screen',
      reading: 'w-full',
      showcase: 'w-full min-h-[80vh]',
      content: 'w-full'
    }

    // 背景样式
    const backgroundClasses = {
      none: '',
      subtle: 'bg-background/50 backdrop-blur-sm',
      gradient: 'bg-gradient-to-br from-background via-background/95 to-background/90',
      glass: 'bg-background/80 backdrop-blur-md border border-border/20'
    }

    // 内边距样式
    const paddingClasses = {
      none: '',
      sm: 'px-4 py-6 sm:px-6 sm:py-8',
      md: 'px-4 py-8 sm:px-6 sm:py-12 lg:px-8 lg:py-16',
      lg: 'px-6 py-12 sm:px-8 sm:py-16 lg:px-12 lg:py-20 xl:px-16 xl:py-24',
      xl: 'px-8 py-16 sm:px-12 sm:py-20 lg:px-16 lg:py-28 xl:px-20 xl:py-32'
    }

    // 最大宽度限制
    const maxWidthClasses = {
      none: '',
      reading: 'max-w-4xl mx-auto',
      wide: 'max-w-7xl mx-auto', 
      ultra: 'max-w-[1920px] mx-auto'
    }

    return (
      <div
        ref={ref}
        className={clsx(
          // 基础样式
          'relative overflow-hidden',
          // 变体样式
          variantClasses[variant],
          // 背景样式
          backgroundClasses[background],
          // 内边距
          paddingClasses[padding],
          // 最大宽度
          maxWidthClasses[maxWidth],
          // 自定义类名
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

FullWidthContainer.displayName = 'FullWidthContainer'

// 🏗️ 区域容器 - 用于页面分区
export const SectionContainer = forwardRef<HTMLElement, SectionContainerProps>(
  ({ 
    variant = 'content', 
    background = 'none', 
    spacing = 'md',
    className, 
    children, 
    ...props 
  }, ref) => {
    
    // 区域变体样式
    const variantClasses = {
      hero: 'relative min-h-screen flex items-center justify-center',
      content: 'relative',
      sidebar: 'relative flex-shrink-0',
      footer: 'relative mt-auto'
    }

    // 背景样式
    const backgroundClasses = {
      none: '',
      subtle: 'bg-muted/30',
      accent: 'bg-accent/10',
      primary: 'bg-primary/5'
    }

    // 间距样式
    const spacingClasses = {
      none: '',
      sm: 'py-8 sm:py-12',
      md: 'py-12 sm:py-16 lg:py-20',
      lg: 'py-16 sm:py-20 lg:py-28',
      xl: 'py-20 sm:py-28 lg:py-36'
    }

    return (
      <section
        ref={ref}
        className={clsx(
          // 基础样式
          'w-full',
          // 变体样式
          variantClasses[variant],
          // 背景样式
          backgroundClasses[background],
          // 间距样式
          spacingClasses[spacing],
          // 自定义类名
          className
        )}
        {...props}
      >
        {children}
      </section>
    )
  }
)

SectionContainer.displayName = 'SectionContainer'

// 📝 内容容器 - 用于内容布局
export const ContentContainer = forwardRef<HTMLDivElement, ContentContainerProps>(
  ({ 
    layout = 'single', 
    align = 'left', 
    spacing = 'normal',
    className, 
    children, 
    ...props 
  }, ref) => {
    
    // 布局样式
    const layoutClasses = {
      single: 'max-w-4xl mx-auto',
      sidebar: 'max-w-7xl mx-auto grid grid-cols-1 xl:grid-cols-[1fr_320px] gap-8 xl:gap-12',
      wide: 'max-w-6xl mx-auto',
      split: 'max-w-7xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12'
    }

    // 对齐样式
    const alignClasses = {
      left: 'text-left',
      center: 'text-center mx-auto',
      right: 'text-right ml-auto'
    }

    // 间距样式
    const spacingClasses = {
      tight: 'space-y-4',
      normal: 'space-y-6',
      loose: 'space-y-8'
    }

    return (
      <div
        ref={ref}
        className={clsx(
          // 基础样式
          'relative',
          // 布局样式
          layoutClasses[layout],
          // 对齐样式
          alignClasses[align],
          // 间距样式
          spacingClasses[spacing],
          // 自定义类名
          className
        )}
        {...props}
      >
        {children}
      </div>
    )
  }
)

ContentContainer.displayName = 'ContentContainer'
