'use client'

import React, { useContext } from 'react'
import { useRouter } from 'next/navigation'
import { ArrowLeftIcon } from '@heroicons/react/24/outline'

import { AppContext } from '@/app/providers'
import { FullWidthContainer, SectionContainer, ContentContainer } from '@/components/layout/FullWidthContainer'
import { Prose } from '@/components/shared/Prose'
import { ReadingProgress } from '@/components/blog/TableOfContents'
import { ResponsiveTableOfContents } from '@/components/blog/ResponsiveTableOfContents'
import { LazyWalineComment } from '@/components/comment/LazyWalineComment'
import { CommentStats } from '@/components/comment/CommentStats'
import { PageViewCount } from '@/components/comment/PageViewCount'
import { type BlogType, type TagType } from '@/lib/blogs'
import { formatDate } from '@/lib/formatDate'
import { tagStyleGenerator } from '@/lib/colorSystem'
import { Calendar, Eye, Heart, User, <PERSON>, BookO<PERSON>, Code } from 'lucide-react'
import { cn } from '@/lib/utils'

// 🎨 全宽博客布局 - 红点奖级别设计
export function FullWidthBlogLayout({
  blog,
  children,
}: {
  blog: BlogType
  children: React.ReactNode
}) {
  let router = useRouter()
  let { previousPathname } = useContext(AppContext)
  const [contentString, setContentString] = React.useState('')

  // 提取内容用于目录生成
  React.useEffect(() => {
    if (React.isValidElement(children) && children.props?.content) {
      setContentString(children.props.content)
    }
  }, [children])

  // 监听DOM变化，确保目录能够正确提取
  React.useEffect(() => {
    const timer = setTimeout(() => {
      const event = new CustomEvent('tocRefresh')
      window.dispatchEvent(event)
    }, 1000)

    return () => clearTimeout(timer)
  }, [contentString])

  return (
    <FullWidthContainer 
      variant="full" 
      background="gradient"
      padding="none"
      className="min-h-screen"
    >
      {/* 阅读进度条 */}
      <ReadingProgress />
      
      {/* 🌟 英雄区域 - 全宽沉浸式头部 */}
      <SectionContainer 
        variant="hero" 
        background="none"
        spacing="none"
        className="relative min-h-[60vh] flex items-center justify-center overflow-hidden"
      >
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/10 via-transparent to-transparent" />
        
        {/* 动态背景粒子效果 */}
        <div className="absolute inset-0 overflow-hidden">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-primary/20 rounded-full animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 3}s`
              }}
            />
          ))}
        </div>

        <ContentContainer layout="wide" align="center" className="relative z-10">
          {/* 返回按钮 */}
          {previousPathname && (
            <div className="absolute top-0 left-0">
              <button
                type="button"
                onClick={() => router.back()}
                aria-label="返回博客列表"
                className="group flex h-12 w-12 items-center justify-center rounded-full bg-background/80 backdrop-blur-sm shadow-lg ring-1 ring-border/20 transition-all duration-300 hover:shadow-xl hover:scale-105 hover:bg-background/90"
              >
                <ArrowLeftIcon className="h-5 w-5 stroke-foreground/70 transition group-hover:stroke-foreground group-hover:-translate-x-0.5" />
              </button>
            </div>
          )}

          {/* 博客头部信息 */}
          <div className="text-center space-y-8 max-w-4xl mx-auto">
            {/* 状态标签 */}
            <div className="flex flex-wrap justify-center gap-3">
              {blog.is_featured && (
                <span className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-amber-500/10 to-orange-500/10 border border-amber-500/20 rounded-full text-amber-700 dark:text-amber-300 hover:scale-105 transition-all duration-200">
                  <Star className="w-4 h-4 fill-current animate-pulse" />
                  精选文章
                </span>
              )}
              {blog.tags && blog.tags.map((tag: TagType) => {
                const tagStyle = tagStyleGenerator(tag.name)
                return (
                  <span
                    key={tag.id}
                    className={cn(
                      "inline-flex items-center px-3 py-1.5 text-xs font-medium rounded-full transition-all duration-200 hover:scale-105",
                      tagStyle.className
                    )}
                    style={tagStyle.style}
                  >
                    {tag.name}
                  </span>
                )
              })}
            </div>

            {/* 标题 */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight bg-gradient-to-r from-foreground via-foreground/90 to-foreground/80 bg-clip-text text-transparent leading-tight">
              {blog.title}
            </h1>

            {/* 描述 */}
            {blog.description && (
              <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
                {blog.description}
              </p>
            )}

            {/* 元信息 */}
            <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <time dateTime={blog.created_at}>
                  {formatDate(blog.created_at)}
                </time>
              </div>
              
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                <PageViewCount path={`/blogs/${blog.slug}`} />
              </div>
              
              <div className="flex items-center gap-2">
                <Heart className="w-4 h-4" />
                <CommentStats path={`/blogs/${blog.slug}`} />
              </div>
              
              <div className="flex items-center gap-2">
                <BookOpen className="w-4 h-4" />
                <span>{Math.ceil((blog.content?.length || 0) / 500)} 分钟阅读</span>
              </div>
            </div>
          </div>
        </ContentContainer>
      </SectionContainer>

      {/* 📖 主内容区域 - 全宽阅读体验 */}
      <SectionContainer variant="content" background="subtle" spacing="lg">
        <ContentContainer layout="sidebar" spacing="loose">
          {/* 主要内容 */}
          <div className="relative">
            <article className="animate-fade-in-up">
              {/* 文章内容 */}
              <div className="article-content" data-mdx-content>
                <Prose className="prose-lg max-w-none">
                  {children}
                </Prose>
              </div>

              {/* 文章结尾装饰 */}
              <div className="mt-16 pt-8 border-t border-border/50">
                <div className="text-center">
                  <div className="inline-flex items-center gap-2 text-muted-foreground">
                    <div className="w-8 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
                    <span className="text-sm">文章结束</span>
                    <div className="w-8 h-px bg-gradient-to-r from-transparent via-border to-transparent" />
                  </div>
                </div>
              </div>
            </article>

            {/* 评论区域 */}
            <section className="mt-20">
              <div className="text-center mb-12">
                <h2 className="text-2xl font-bold mb-4">参与讨论</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  您的想法和见解很宝贵。分享您的观点，与其他读者交流。
                </p>
              </div>

              <LazyWalineComment
                path={`/blogs/${blog.slug}`}
                title={blog.title}
                className="max-w-4xl mx-auto"
              />
            </section>
          </div>

          {/* 侧边栏 - 目录和相关信息 */}
          <aside className="relative">
            {/* 桌面端固定目录 */}
            <div className="sticky top-8 space-y-6">
              <ResponsiveTableOfContents content={contentString} />
              
              {/* 文章信息卡片 */}
              <div className="p-6 bg-background/60 backdrop-blur-sm border border-border/50 rounded-2xl">
                <h3 className="font-semibold mb-4">文章信息</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">发布时间</span>
                    <span>{formatDate(blog.created_at)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">字数统计</span>
                    <span>{blog.content?.length || 0} 字</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">阅读时长</span>
                    <span>{Math.ceil((blog.content?.length || 0) / 500)} 分钟</span>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        </ContentContainer>
      </SectionContainer>

      {/* 移动端目录 */}
      <div className="xl:hidden">
        <SectionContainer variant="content" background="none" spacing="sm">
          <ContentContainer layout="single">
            <ResponsiveTableOfContents content={contentString} />
          </ContentContainer>
        </SectionContainer>
      </div>
    </FullWidthContainer>
  )
}
