'use client'

import React, { useState } from 'react'
import { FullWidthContainer, SectionContainer, ContentContainer } from '@/components/layout/FullWidthContainer'
import { Container } from '@/components/layout/Container'
import { Monitor, Smartphone, Tablet, Eye, Zap, Layout, Palette } from 'lucide-react'

// 🎨 全宽布局演示页面
export default function FullWidthLayoutDemo() {
  const [currentLayout, setCurrentLayout] = useState<'current' | 'fullwidth'>('fullwidth')
  const [viewMode, setViewMode] = useState<'desktop' | 'tablet' | 'mobile'>('desktop')

  // 模拟博客数据
  const mockBlog = {
    title: "全宽布局设计系统：重新定义现代Web体验",
    description: "探索如何通过全宽布局设计创造震撼的视觉体验，充分利用屏幕空间，提升用户沉浸感。",
    content: "这是一篇关于全宽布局设计的详细文章内容...",
    tags: ["设计系统", "用户体验", "前端开发"],
    created_at: "2024-08-04",
    reading_time: 8
  }

  const viewModeClasses = {
    desktop: 'w-full',
    tablet: 'w-[768px] mx-auto',
    mobile: 'w-[375px] mx-auto'
  }

  return (
    <div className="min-h-screen bg-background">
      {/* 控制面板 */}
      <div className="fixed top-4 right-4 z-50 bg-background/90 backdrop-blur-sm border border-border/50 rounded-2xl p-4 shadow-xl">
        <div className="space-y-4">
          {/* 布局切换 */}
          <div>
            <label className="text-sm font-medium mb-2 block">布局模式</label>
            <div className="flex gap-2">
              <button
                onClick={() => setCurrentLayout('current')}
                className={`px-3 py-1.5 text-xs rounded-lg transition-all ${
                  currentLayout === 'current' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted hover:bg-muted/80'
                }`}
              >
                当前布局
              </button>
              <button
                onClick={() => setCurrentLayout('fullwidth')}
                className={`px-3 py-1.5 text-xs rounded-lg transition-all ${
                  currentLayout === 'fullwidth' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted hover:bg-muted/80'
                }`}
              >
                全宽布局
              </button>
            </div>
          </div>

          {/* 视图模式 */}
          <div>
            <label className="text-sm font-medium mb-2 block">设备预览</label>
            <div className="flex gap-2">
              <button
                onClick={() => setViewMode('desktop')}
                className={`p-2 rounded-lg transition-all ${
                  viewMode === 'desktop' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted hover:bg-muted/80'
                }`}
              >
                <Monitor className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('tablet')}
                className={`p-2 rounded-lg transition-all ${
                  viewMode === 'tablet' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted hover:bg-muted/80'
                }`}
              >
                <Tablet className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('mobile')}
                className={`p-2 rounded-lg transition-all ${
                  viewMode === 'mobile' 
                    ? 'bg-primary text-primary-foreground' 
                    : 'bg-muted hover:bg-muted/80'
                }`}
              >
                <Smartphone className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 布局演示区域 */}
      <div className={`transition-all duration-500 ${viewModeClasses[viewMode]}`}>
        {currentLayout === 'current' ? (
          // 当前布局演示
          <CurrentLayoutDemo blog={mockBlog} />
        ) : (
          // 全宽布局演示
          <FullWidthLayoutDemo blog={mockBlog} />
        )}
      </div>

      {/* 对比说明 */}
      <div className="fixed bottom-4 left-4 right-4 bg-background/90 backdrop-blur-sm border border-border/50 rounded-2xl p-6 shadow-xl max-w-4xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* 当前布局特点 */}
          <div className={`space-y-3 transition-all duration-300 ${
            currentLayout === 'current' ? 'opacity-100' : 'opacity-50'
          }`}>
            <h3 className="font-semibold flex items-center gap-2">
              <Layout className="w-4 h-4" />
              当前布局特点
            </h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 居中固定宽度容器</li>
              <li>• 屏幕利用率 60-70%</li>
              <li>• 传统阅读体验</li>
              <li>• 两侧留白较多</li>
            </ul>
          </div>

          {/* 全宽布局优势 */}
          <div className={`space-y-3 transition-all duration-300 ${
            currentLayout === 'fullwidth' ? 'opacity-100' : 'opacity-50'
          }`}>
            <h3 className="font-semibold flex items-center gap-2">
              <Zap className="w-4 h-4" />
              全宽布局优势
            </h3>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• 充分利用屏幕空间</li>
              <li>• 屏幕利用率 90-95%</li>
              <li>• 沉浸式视觉体验</li>
              <li>• 现代化设计语言</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}

// 当前布局演示组件
function CurrentLayoutDemo({ blog }: { blog: any }) {
  return (
    <div className="bg-background min-h-screen">
      <Container className="mt-16 lg:mt-32">
        <div className="xl:relative">
          <div className="mx-auto max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl">
            <article className="animate-fade-in-up">
              {/* 传统头部 */}
              <header className="relative mb-12 p-8 rounded-3xl bg-gradient-to-r from-background/80 via-background/90 to-background/80 border border-border/50 backdrop-blur-sm shadow-lg">
                <div className="space-y-6">
                  <div className="flex flex-wrap gap-2">
                    {blog.tags.map((tag: string, index: number) => (
                      <span key={index} className="inline-flex items-center px-3 py-1.5 text-xs font-medium bg-primary/10 text-primary rounded-full">
                        {tag}
                      </span>
                    ))}
                  </div>
                  
                  <h1 className="text-3xl sm:text-4xl font-bold tracking-tight">
                    {blog.title}
                  </h1>
                  
                  <p className="text-lg text-muted-foreground">
                    {blog.description}
                  </p>
                  
                  <div className="flex items-center gap-4 text-sm text-muted-foreground">
                    <span>{blog.created_at}</span>
                    <span>•</span>
                    <span>{blog.reading_time} 分钟阅读</span>
                  </div>
                </div>
              </header>

              {/* 内容区域 */}
              <div className="prose prose-lg max-w-none">
                <p>这是传统布局的内容区域。内容被限制在固定宽度的容器中，两侧有较多留白。</p>
                <p>虽然这种布局在阅读体验上是可接受的，但在现代设计趋势下显得保守，没有充分利用屏幕空间。</p>
              </div>
            </article>
          </div>
        </div>
      </Container>
    </div>
  )
}

// 全宽布局演示组件
function FullWidthLayoutDemo({ blog }: { blog: any }) {
  return (
    <FullWidthContainer variant="full" background="gradient" padding="none">
      {/* 英雄区域 */}
      <SectionContainer variant="hero" background="none" spacing="none" className="relative min-h-[60vh] flex items-center justify-center overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--tw-gradient-stops))] from-primary/10 via-transparent to-transparent" />
        
        {/* 动态背景粒子 */}
        <div className="absolute inset-0 overflow-hidden">
          {Array.from({ length: 15 }).map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-primary/20 rounded-full animate-pulse"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 3}s`,
                animationDuration: `${2 + Math.random() * 3}s`
              }}
            />
          ))}
        </div>

        <ContentContainer layout="wide" align="center" className="relative z-10">
          <div className="text-center space-y-8 max-w-4xl mx-auto">
            {/* 标签 */}
            <div className="flex flex-wrap justify-center gap-3">
              {blog.tags.map((tag: string, index: number) => (
                <span key={index} className="inline-flex items-center gap-2 px-4 py-2 text-sm font-medium bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 rounded-full text-primary hover:scale-105 transition-all duration-200">
                  {tag}
                </span>
              ))}
            </div>

            {/* 标题 */}
            <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight bg-gradient-to-r from-foreground via-foreground/90 to-foreground/80 bg-clip-text text-transparent leading-tight">
              {blog.title}
            </h1>

            {/* 描述 */}
            <p className="text-lg sm:text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              {blog.description}
            </p>

            {/* 元信息 */}
            <div className="flex flex-wrap justify-center items-center gap-6 text-sm text-muted-foreground">
              <div className="flex items-center gap-2">
                <Eye className="w-4 h-4" />
                <span>1,234 次阅读</span>
              </div>
              <div className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                <span>{blog.reading_time} 分钟阅读</span>
              </div>
            </div>
          </div>
        </ContentContainer>
      </SectionContainer>

      {/* 主内容区域 */}
      <SectionContainer variant="content" background="subtle" spacing="lg">
        <ContentContainer layout="sidebar" spacing="loose">
          {/* 主要内容 */}
          <div className="relative">
            <div className="prose prose-lg max-w-none">
              <p>这是全宽布局的内容区域。通过充分利用屏幕宽度，我们可以创造更加沉浸式的阅读体验。</p>
              <p>侧边栏区域可以展示目录、相关信息等辅助内容，而主内容区域则有更宽敞的空间来展示文章内容。</p>
              <p>这种布局特别适合现代的宽屏显示器，能够显著提升用户的视觉体验和信息获取效率。</p>
            </div>
          </div>

          {/* 侧边栏 */}
          <aside className="relative">
            <div className="sticky top-8 space-y-6">
              {/* 目录卡片 */}
              <div className="p-6 bg-background/60 backdrop-blur-sm border border-border/50 rounded-2xl">
                <h3 className="font-semibold mb-4">文章目录</h3>
                <div className="space-y-2 text-sm">
                  <div className="text-primary cursor-pointer hover:text-primary/80">1. 设计理念</div>
                  <div className="text-muted-foreground cursor-pointer hover:text-foreground">2. 布局系统</div>
                  <div className="text-muted-foreground cursor-pointer hover:text-foreground">3. 响应式设计</div>
                  <div className="text-muted-foreground cursor-pointer hover:text-foreground">4. 实施方案</div>
                </div>
              </div>

              {/* 信息卡片 */}
              <div className="p-6 bg-background/60 backdrop-blur-sm border border-border/50 rounded-2xl">
                <h3 className="font-semibold mb-4">文章信息</h3>
                <div className="space-y-3 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">发布时间</span>
                    <span>{blog.created_at}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">阅读时长</span>
                    <span>{blog.reading_time} 分钟</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">字数统计</span>
                    <span>2,500 字</span>
                  </div>
                </div>
              </div>
            </div>
          </aside>
        </ContentContainer>
      </SectionContainer>
    </FullWidthContainer>
  )
}
